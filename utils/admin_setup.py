"""
Утилиты для настройки админов системы
"""
import logging
from typing import Optional
from database import get_db_session
from database.models import User
from database.repositories.user_repository import UserRepository
from sqlalchemy import select, func


async def has_any_admin() -> bool:
    """Проверить, есть ли хотя бы один админ в системе"""
    try:
        async with get_db_session() as session:
            result = await session.execute(
                select(func.count(User.id)).where(User.role == 'admin')
            )
            admin_count = result.scalar()
            return admin_count > 0
    except Exception as e:
        logging.error(f"❌ Ошибка при проверке админов: {e}")
        return False


async def create_first_admin(telegram_id: int, name: str) -> Optional[User]:
    """
    Создать первого админа в системе.
    Работает только если в системе еще нет ни одного админа.
    """
    try:
        # Проверяем, есть ли уже админы
        if await has_any_admin():
            logging.warning(f"❌ Попытка создать первого админа, но админы уже существуют")
            return None
        
        # Проверяем, существует ли пользователь с таким Telegram ID
        existing_user = await UserRepository.get_by_telegram_id(telegram_id)
        if existing_user:
            # Если пользователь существует, обновляем его роль на админа
            async with get_db_session() as session:
                existing_user.role = 'admin'
                session.add(existing_user)
                await session.commit()
                await session.refresh(existing_user)
                logging.info(f"✅ Пользователь {existing_user.name} назначен первым админом")
                return existing_user
        else:
            # Создаем нового пользователя с ролью админа
            admin_user = await UserRepository.create(
                telegram_id=telegram_id,
                name=name,
                role='admin'
            )
            logging.info(f"✅ Создан первый админ: {admin_user.name} (ID: {admin_user.telegram_id})")
            return admin_user
            
    except Exception as e:
        logging.error(f"❌ Ошибка при создании первого админа: {e}")
        return None


async def get_admin_count() -> int:
    """Получить количество админов в системе"""
    try:
        async with get_db_session() as session:
            result = await session.execute(
                select(func.count(User.id)).where(User.role == 'admin')
            )
            return result.scalar()
    except Exception as e:
        logging.error(f"❌ Ошибка при подсчете админов: {e}")
        return 0


async def get_all_admins() -> list:
    """Получить список всех админов"""
    try:
        async with get_db_session() as session:
            result = await session.execute(
                select(User).where(User.role == 'admin').order_by(User.name)
            )
            return list(result.scalars().all())
    except Exception as e:
        logging.error(f"❌ Ошибка при получении списка админов: {e}")
        return []


async def is_user_admin(telegram_id: int) -> bool:
    """Проверить, является ли пользователь админом"""
    try:
        user = await UserRepository.get_by_telegram_id(telegram_id)
        return user is not None and user.role == 'admin'
    except Exception as e:
        logging.error(f"❌ Ошибка при проверке роли пользователя: {e}")
        return False


def get_setup_admin_message() -> str:
    """Получить сообщение для настройки первого админа"""
    return (
        "🔧 **Настройка системы**\n\n"
        "В системе еще нет ни одного администратора.\n"
        "Для начала работы необходимо назначить первого админа.\n\n"
        "Отправьте команду в формате:\n"
        "`/setup_admin <telegram_id> <имя>`\n\n"
        "Например:\n"
        "`/setup_admin 123456789 Иван Иванов`\n\n"
        "⚠️ Эта команда работает только один раз - "
        "для создания самого первого админа в системе."
    )
