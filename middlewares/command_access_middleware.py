"""
Middleware для контроля доступа к командам в зависимости от роли пользователя
"""
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import Message
import logging


class CommandAccessMiddleware(BaseMiddleware):
    """Middleware для ограничения доступа к командам ролей"""

    def __init__(self):
        # Команды, доступные только админам
        self.admin_only_commands = {
            '/admin', '/manager', '/curator', '/teacher', '/student'
        }

    async def __call__(
        self,
        handler: Callable[[Message, Dict[str, Any]], Awaitable[Any]],
        event: Message,
        data: Dict[str, Any]
    ) -> Any:
        # Проверяем только текстовые сообщения с командами
        if not event.text or not event.text.startswith('/'):
            return await handler(event, data)

        command = event.text.split()[0].lower()
        user_role = data.get("user_role", "new_user")
        user_id = data.get("user_id", event.from_user.id)

        # Если это команда роли и пользователь не админ
        if command in self.admin_only_commands and user_role != "admin":
            logging.warning(f"🚫 Пользователь {user_id} (роль: {user_role}) попытался использовать команду {command}")
            
            await event.answer(
                "❌ У вас нет доступа к этой команде.\n"
                "Используйте /start для перехода в главное меню.",
                show_alert=False
            )
            return  # Прерываем обработку

        # Продолжаем обработку для разрешенных команд
        return await handler(event, data)
